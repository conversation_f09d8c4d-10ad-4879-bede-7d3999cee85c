package com.wendao101.order.service;

import com.wendao101.order.domain.ShopGotowxWhitelist;

import java.util.List;

public interface IShopGotowxWhitelistService {

    /**
     * 查询购课后去微信看课白名单
     *
     * @param id 主键id
     * @return 购课后去微信看课白名单
     */
    ShopGotowxWhitelist selectShopGotowxWhitelistById(Long id);

    /**
     * 查询购课后去微信看课白名单列表
     *
     * @param shopGotowxWhitelist 购课后去微信看课白名单
     * @return 购课后去微信看课白名单集合
     */
    List<ShopGotowxWhitelist> selectShopGotowxWhitelistList(ShopGotowxWhitelist shopGotowxWhitelist);

    /**
     * 新增购课后去微信看课白名单
     *
     * @param shopGotowxWhitelist 购课后去微信看课白名单
     * @return 结果
     */
    int insertShopGotowxWhitelist(ShopGotowxWhitelist shopGotowxWhitelist);

    /**
     * 修改购课后去微信看课白名单
     *
     * @param shopGotowxWhitelist 购课后去微信看课白名单
     * @return 结果
     */
    int updateShopGotowxWhitelist(ShopGotowxWhitelist shopGotowxWhitelist);

    /**
     * 删除购课后去微信看课白名单
     *
     * @param id 主键id
     * @return 结果
     */
    int deleteShopGotowxWhitelistById(Long id);

    /**
     * 批量删除购课后去微信看课白名单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteShopGotowxWhitelistByIds(Long[] ids);

    /**
     * 根据老师ID查询购课后去微信看课白名单
     *
     * @param teacherId 老师ID
     * @return 购课后去微信看课白名单
     */
    ShopGotowxWhitelist selectShopGotowxWhitelistByTeacherId(Long teacherId);
}
